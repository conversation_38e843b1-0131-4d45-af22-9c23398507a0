// Temu 店铺信息检测器
// 这个脚本会在 Temu 商家后台页面中运行，主动获取店铺信息并保存到 Chrome Storage

interface TemuSiteInfo {
  fromPlat: string
  mallId: string | number
  shopId: string | number
  mallName: string
  shopName: string
  mallStatus: number
  isSemiManagedMall: boolean
  logo: string
}

class TemuShopDetector {
  private shopInfo: TemuSiteInfo | null = null
  private isProcessing = false
  private maxRetries = 3
  private retryCount = 0
  private cachedAntiContent: string | null = null
  private antiContentExpiry: number = 0
  private cachedMallId: string | null = null
  private mallIdExpiry: number = 0

  constructor() {
    this.setupRequestInterception()
    this.loadCachedHeaders()
    this.init()
  }

  // 设置请求拦截来缓存anti-content和mallid（简化版，主要依赖早期拦截器）
  private setupRequestInterception() {
    try {
      console.info('[Temu Detector] 设置简化请求拦截器...')

      // 检查早期拦截器是否已经工作
      const earlyInterceptor = (window as any).temuEarlyInterceptor
      if (earlyInterceptor) {
        console.info('[Temu Detector] 早期拦截器已存在，跳过重复设置')
        return
      }

      // 如果早期拦截器不存在，设置基本的fetch拦截
      const originalFetch = window.fetch
      const self = this

      window.fetch = async function(...args) {
        const [url, options] = args

        // 只检查Temu API请求
        if (options && options.headers && typeof url === 'string' && url.includes('seller.kuajingmaihuo.com')) {
          console.info('[Temu Detector] 拦截到fetch请求:', url)
          self.cacheHeadersFromRequest(options.headers as Record<string, string>)
        }

        const response = await originalFetch.apply(this, args)
        return response
      }

      console.info('[Temu Detector] 简化请求拦截器设置完成')
    } catch (error) {
      console.warn('[Temu Detector] 设置请求拦截器失败:', error)
    }
  }

  // 从请求头中缓存数据（精确匹配）
  private cacheHeadersFromRequest(headers: Record<string, string>) {
    // 基于成功的拦截结果，使用精确的键名匹配
    if (headers['anti-content']) {
      console.info('[Temu Detector] 🎯 从请求头缓存 anti-content')
      this.cacheAntiContent(headers['anti-content'])
    }
    if (headers['mallid']) {
      console.info('[Temu Detector] 🎯 从请求头缓存 mallid')
      this.cacheMallId(headers['mallid'])
    }
  }

  // 缓存anti-content
  private cacheAntiContent(antiContent: string) {
    if (antiContent && antiContent !== this.cachedAntiContent) {
      this.cachedAntiContent = antiContent
      this.antiContentExpiry = Date.now() + 30 * 60 * 1000 // 30分钟过期

      // 保存到localStorage
      try {
        localStorage.setItem('temu_cached_anti_content', antiContent)
        localStorage.setItem('temu_anti_content_expiry', this.antiContentExpiry.toString())
        console.info('[Temu Detector] ✅ 缓存 anti-content 成功')
      } catch (error) {
        console.warn('[Temu Detector] 缓存 anti-content 失败:', error)
      }
    }
  }

  // 缓存mallid
  private cacheMallId(mallId: string) {
    if (mallId && mallId !== this.cachedMallId) {
      this.cachedMallId = mallId
      this.mallIdExpiry = Date.now() + 60 * 60 * 1000 // 1小时过期

      // 保存到localStorage
      try {
        localStorage.setItem('temu_cached_mall_id', mallId)
        localStorage.setItem('temu_mall_id_expiry', this.mallIdExpiry.toString())
        console.info('[Temu Detector] ✅ 缓存 mallId 成功:', mallId)
      } catch (error) {
        console.warn('[Temu Detector] 缓存 mallId 失败:', error)
      }
    }
  }

  // 从localStorage加载缓存的头部信息
  private loadCachedHeaders() {
    try {
      // 加载anti-content
      const cachedAntiContent = localStorage.getItem('temu_cached_anti_content')
      const antiContentExpiry = localStorage.getItem('temu_anti_content_expiry')

      if (cachedAntiContent && antiContentExpiry) {
        const expiry = parseInt(antiContentExpiry)
        if (Date.now() < expiry) {
          this.cachedAntiContent = cachedAntiContent
          this.antiContentExpiry = expiry
          console.info('[Temu Detector] 从缓存加载 anti-content:', cachedAntiContent.substring(0, 50) + '...')
        } else {
          // 过期了，清除缓存
          localStorage.removeItem('temu_cached_anti_content')
          localStorage.removeItem('temu_anti_content_expiry')
        }
      }

      // 加载mallId
      const cachedMallId = localStorage.getItem('temu_cached_mall_id')
      const mallIdExpiry = localStorage.getItem('temu_mall_id_expiry')

      if (cachedMallId && mallIdExpiry) {
        const expiry = parseInt(mallIdExpiry)
        if (Date.now() < expiry) {
          this.cachedMallId = cachedMallId
          this.mallIdExpiry = expiry
          console.info('[Temu Detector] 从缓存加载 mallId:', cachedMallId)
        } else {
          // 过期了，清除缓存
          localStorage.removeItem('temu_cached_mall_id')
          localStorage.removeItem('temu_mall_id_expiry')
        }
      }
    } catch (error) {
      console.warn('[Temu Detector] 加载缓存头部信息失败:', error)
    }
  }

  private init() {
    console.info('[Temu Detector] 初始化检测器...')

    // 等待页面加载完成后开始检测
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.startDetection())
    } else {
      // 页面已加载，延迟一下再开始检测，确保页面完全渲染
      setTimeout(() => this.startDetection(), 1000)
    }
  }

  private async startDetection() {
    if (this.isProcessing) {
      console.info('[Temu Detector] 检测正在进行中，跳过...')
      return
    }

    this.isProcessing = true
    console.info('[Temu Detector] 开始检测店铺信息...')

    try {
      // 主动调用 Temu API 获取用户信息
      const result = await this.fetchTemuUserInfo()

      if (result) {
        this.shopInfo = result
        await this.saveToStorage(result)
        this.notifyExtension(result)
        console.info('[Temu Detector] 成功检测并保存店铺信息:', result)
      } else {
        // 如果失败，尝试重试
        this.retryDetection()
      }
    } catch (error) {
      console.error('[Temu Detector] 检测过程中出错:', error)
      this.retryDetection()
    } finally {
      this.isProcessing = false
    }
  }

  private async retryDetection() {
    if (this.retryCount >= this.maxRetries) {
      console.warn('[Temu Detector] 达到最大重试次数，停止检测')
      return
    }

    this.retryCount++
    console.info(`[Temu Detector] 第 ${this.retryCount} 次重试检测...`)

    setTimeout(() => {
      this.startDetection()
    }, 3000 * this.retryCount) // 递增延迟：3秒、6秒、9秒
  }

  // 获取页面中的 anti-content 头部
  public getAntiContentHeader(): string | null {
    try {
      console.info('[Temu Detector] 开始获取 anti-content 头部...')

      // 1. 优先使用早期拦截器的缓存
      const earlyInterceptor = (window as any).temuEarlyInterceptor
      if (earlyInterceptor) {
        const earlyAntiContent = earlyInterceptor.getCachedAntiContent()
        if (earlyAntiContent) {
          console.info('[Temu Detector] 使用早期拦截器缓存的 anti-content:', earlyAntiContent.substring(0, 50) + '...')
          return earlyAntiContent
        }
      }

      // 2. 使用本地缓存的anti-content（如果未过期）
      if (this.cachedAntiContent && Date.now() < this.antiContentExpiry) {
        console.info('[Temu Detector] 使用本地缓存的 anti-content:', this.cachedAntiContent.substring(0, 50) + '...')
        return this.cachedAntiContent
      }

      // 3. 如果缓存过期或不存在，尝试从页面获取
      const antiContent = this.getAntiContentFromPage()
      if (antiContent) {
        // 缓存找到的anti-content
        this.cacheAntiContent(antiContent)
        console.info('[Temu Detector] 从页面获取到 anti-content:', antiContent.substring(0, 50) + '...')
        return antiContent
      }

      console.warn('[Temu Detector] 未能找到 anti-content，建议等待页面发起API请求后再试')
      return null
    } catch (error) {
      console.error('[Temu Detector] 获取 anti-content 时出错:', error)
      return null
    }
  }

  // 获取缓存的mallId
  public getCachedMallId(): string | null {
    // 1. 优先使用早期拦截器的缓存
    const earlyInterceptor = (window as any).temuEarlyInterceptor
    if (earlyInterceptor) {
      const earlyMallId = earlyInterceptor.getCachedMallId()
      if (earlyMallId) {
        console.info('[Temu Detector] 使用早期拦截器缓存的 mallId:', earlyMallId)
        return earlyMallId
      }
    }

    // 2. 使用本地缓存
    if (this.cachedMallId && Date.now() < this.mallIdExpiry) {
      console.info('[Temu Detector] 使用本地缓存的 mallId:', this.cachedMallId)
      return this.cachedMallId
    }

    console.warn('[Temu Detector] 没有有效的缓存 mallId')
    return null
  }

  // 从页面中获取 anti-content 值（增强调试版本）
  private getAntiContentFromPage(): string | null {
    try {
      console.info('[Temu Detector] 开始获取 anti-content...')

      // 首先进行全面的页面内容调试
      this.debugPageContent()

      // 方法1: 从页面的全局变量中获取
      const win = window as any
      console.info('[Temu Detector] 检查全局变量...')
      console.info('[Temu Detector] window.__ANTI_CONTENT__:', win.__ANTI_CONTENT__)
      console.info('[Temu Detector] window.__INITIAL_STATE__:', win.__INITIAL_STATE__)
      console.info('[Temu Detector] window.g_config:', win.g_config)

      if (win.__ANTI_CONTENT__) {
        console.info('[Temu Detector] 从全局变量获取到 anti-content')
        return win.__ANTI_CONTENT__
      }

      // 方法2: 从页面的meta标签中获取
      const metaTag = document.querySelector('meta[name="anti-content"]')
      console.info('[Temu Detector] meta标签检查结果:', metaTag)
      if (metaTag) {
        const content = metaTag.getAttribute('content')
        if (content) {
          console.info('[Temu Detector] 从meta标签获取到 anti-content')
          return content
        }
      }

      // 方法3: 从localStorage中获取
      const storedAntiContent = localStorage.getItem('anti-content')
      console.info('[Temu Detector] localStorage检查结果:', storedAntiContent)
      if (storedAntiContent) {
        console.info('[Temu Detector] 从localStorage获取到 anti-content')
        return storedAntiContent
      }

      // 方法4: 尝试从页面的script标签中提取
      const scripts = document.querySelectorAll('script')
      console.info('[Temu Detector] 找到script标签数量:', scripts.length)

      let scriptIndex = 0
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML
        if (!content) continue

        scriptIndex++

        // 尝试多种模式匹配
        const patterns = [
          /anti-content['"]\s*:\s*['"]([^'"]+)['"]/i,
          /"anti-content"\s*:\s*"([^"]+)"/i,
          /'anti-content'\s*:\s*'([^']+)'/i,
          /antiContent['"]\s*:\s*['"]([^'"]+)['"]/i,
          /ANTI_CONTENT['"]\s*:\s*['"]([^'"]+)['"]/i,
          /["']anti-content["']\s*:\s*["']([^"']+)["']/i
        ]

        for (const pattern of patterns) {
          const match = content.match(pattern)
          if (match) {
            console.info(`[Temu Detector] 在第${scriptIndex}个script标签中找到 anti-content:`, match[1])
            return match[1]
          }
        }

        // 如果script内容包含anti-content关键词，输出调试信息
        if (content.toLowerCase().includes('anti-content') || content.toLowerCase().includes('anticontent')) {
          console.info(`[Temu Detector] 第${scriptIndex}个script包含anti-content关键词，内容片段:`, content.substring(0, 500))
        }
      }

      console.warn('[Temu Detector] 未能找到 anti-content')
      return null
    } catch (error) {
      console.error('[Temu Detector] 获取 anti-content 失败:', error)
      return null
    }
  }

  // 调试页面内容
  private debugPageContent(): void {
    try {
      console.info('[Temu Detector] === 页面内容调试开始 ===')

      // 检查页面URL
      console.info('[Temu Detector] 当前页面URL:', window.location.href)

      // 检查页面标题
      console.info('[Temu Detector] 页面标题:', document.title)

      // 检查所有meta标签
      const allMetas = document.querySelectorAll('meta')
      console.info('[Temu Detector] 所有meta标签数量:', allMetas.length)
      allMetas.forEach((meta, index) => {
        const name = meta.getAttribute('name')
        const property = meta.getAttribute('property')
        const content = meta.getAttribute('content')
        if (name || property) {
          console.info(`[Temu Detector] Meta ${index + 1}:`, { name, property, content: content?.substring(0, 100) })
        }
      })

      // 检查localStorage中的所有键
      console.info('[Temu Detector] localStorage键列表:')
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key) {
          const value = localStorage.getItem(key)
          console.info(`[Temu Detector] localStorage[${key}]:`, value?.substring(0, 100))
        }
      }

      // 检查sessionStorage中的所有键
      console.info('[Temu Detector] sessionStorage键列表:')
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i)
        if (key) {
          const value = sessionStorage.getItem(key)
          console.info(`[Temu Detector] sessionStorage[${key}]:`, value?.substring(0, 100))
        }
      }

      // 检查window对象的关键属性
      const win = window as any
      const keyProps = ['__ANTI_CONTENT__', '__INITIAL_STATE__', 'g_config', '__APP_STATE__', '__TEMU_STATE__']
      keyProps.forEach(prop => {
        if (win[prop] !== undefined) {
          console.info(`[Temu Detector] window.${prop}:`, typeof win[prop], win[prop])
        }
      })

      console.info('[Temu Detector] === 页面内容调试结束 ===')
    } catch (error) {
      console.error('[Temu Detector] 页面内容调试失败:', error)
    }
  }

  // 主动调用 Temu API 获取用户信息
  private async fetchTemuUserInfo(): Promise<TemuSiteInfo | null> {
    try {
      console.info('[Temu Detector] 主动调用 Temu API...')

      // 获取 anti-content 头部
      const antiContent = this.getAntiContentHeader()
      console.info('[Temu Detector] Anti-content 头部:', antiContent ? '已获取' : '未找到')

      // 构建请求头
      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0',
        'Origin': 'https://seller.kuajingmaihuo.com',
        'Referer': 'https://seller.kuajingmaihuo.com/',
        'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': navigator.userAgent
      }

      // 如果找到了 anti-content，添加到请求头中
      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      // 调用 Temu 的用户信息 API
      const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST', // 改为 POST 请求，因为正常请求是 POST
        credentials: 'include', // 包含 cookies
        headers,
        body: JSON.stringify({}) // 发送空的 JSON 对象
      })

      console.info('[Temu Detector] API 响应状态:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        console.info('[Temu Detector] API 返回数据:', data)

        const shopInfo = this.parseShopData(data)
        if (shopInfo) {
          console.info('[Temu Detector] 成功解析店铺信息:', shopInfo)
          return shopInfo
        } else {
          console.warn('[Temu Detector] 无法从 API 数据中解析店铺信息')
        }
      } else {
        console.warn('[Temu Detector] API 调用失败:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('[Temu Detector] API 调用出错:', error)
    }

    return null
  }

  // 保存店铺信息到 Chrome Storage
  private async saveToStorage(shopInfo: TemuSiteInfo): Promise<void> {
    try {
      console.info('[Temu Detector] 开始保存店铺信息到存储...')

      if (typeof chrome !== 'undefined' && chrome.storage) {
        // 保存到 Chrome Storage
        await chrome.storage.local.set({
          temuSiteInfo: shopInfo,
          shop_binding_state: {
            isTemuBound: true,
            temuSiteInfo: shopInfo,
            lastCheckTime: new Date().toISOString()
          }
        })
        console.info('[Temu Detector] 数据已保存到 Chrome Storage')
      } else {
        // 降级到 localStorage
        localStorage.setItem('temuSiteInfo', JSON.stringify(shopInfo))
        localStorage.setItem('shop_binding_state', JSON.stringify({
          isTemuBound: true,
          temuSiteInfo: shopInfo,
          lastCheckTime: new Date().toISOString()
        }))
        console.info('[Temu Detector] 数据已保存到 LocalStorage')
      }
    } catch (error) {
      console.error('[Temu Detector] 保存数据失败:', error)
    }
  }

  // 解析 Temu API 返回的数据
  private parseShopData(data: unknown): TemuSiteInfo | null {
    if (!data || typeof data !== 'object') return null

    const apiData = data as Record<string, unknown>

    // 处理 Temu API 的标准返回格式
    // 格式: { success: true, result: { companyList: [{ malInfoList: [...] }] } }
    if (apiData.success && apiData.result) {
      const result = apiData.result as Record<string, unknown>

      // 检查 companyList 中的 malInfoList
      if (result.companyList && Array.isArray(result.companyList)) {
        for (const company of result.companyList) {
          if (company && typeof company === 'object') {
            const companyData = company as Record<string, unknown>
            if (companyData.malInfoList && Array.isArray(companyData.malInfoList)) {
              for (const mallInfo of companyData.malInfoList) {
                if (mallInfo && typeof mallInfo === 'object') {
                  const mallData = mallInfo as Record<string, unknown>
                  if (mallData.mallId && mallData.mallName) {
                    return {
                      fromPlat: 'temu',
                      mallId: mallData.mallId as string | number,
                      shopId: mallData.mallId as string | number, // 在 Temu 中 mallId 就是 shopId
                      mallName: mallData.mallName as string,
                      shopName: mallData.mallName as string,
                      mallStatus: (mallData.mallStatus as number) || 1,
                      isSemiManagedMall: (mallData.isSemiManagedMall as boolean) || false,
                      logo: (mallData.logo as string) || ''
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    console.warn('[Temu Detector] 无法解析 API 数据格式')
    return null
  }

  // 通知扩展程序检测到店铺信息
  private notifyExtension(shopInfo: TemuSiteInfo): void {
    console.info('[Temu Detector] 通知扩展程序检测到店铺信息...')

    // 通过 postMessage 发送消息
    window.postMessage({
      type: 'TEMU_SHOP_DETECTED',
      data: shopInfo
    }, '*')

    // 通过 chrome.runtime.sendMessage 发送给背景脚本
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'TEMU_SHOP_DETECTED',
        data: shopInfo
      }).catch(() => {
        // 忽略错误，可能是在非扩展环境中
        console.warn('[Temu Detector] 无法发送消息到背景脚本')
      })
    }
  }

  // 公共方法：手动触发检测
  public async manualDetect(): Promise<TemuSiteInfo | null> {
    console.info('[Temu Detector] 手动触发检测...')
    this.retryCount = 0
    return await this.fetchTemuUserInfo()
  }

  // 公共方法：获取原始 API 数据（用于 side panel 通信）
  public async getRawApiData(): Promise<unknown> {
    try {
      console.info('[Temu Detector] 获取原始 API 数据...')

      // 获取 anti-content 头部
      const antiContent = this.getAntiContentHeader()
      console.info('[Temu Detector] Anti-content 头部:', antiContent ? '已获取' : '未找到')

      // 构建请求头
      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0'
      }

      // 如果找到了 anti-content，添加到请求头中
      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify({})
      })

      if (response.ok) {
        const data = await response.json()
        console.info('[Temu Detector] 原始 API 响应:', data)

        if (data.success && data.result) {
          return data.result // 返回原始的 result 数据
        } else {
          throw new Error('API 返回数据格式错误')
        }
      } else {
        throw new Error(`API 请求失败: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.error('[Temu Detector] 获取原始 API 数据失败:', error)
      throw error
    }
  }

  // 获取 Temu 数据（商品列表、待办事项等）
  public async getTemuData(apiType: string, params: any, mallId?: string): Promise<any> {
    try {
      console.info('[Temu Detector] 开始获取 Temu 数据...', { apiType, params, mallId })

      // 获取 anti-content 头部
      const antiContent = this.getAntiContentHeader()

      // 获取 mallId（优先使用传入的，然后使用缓存的）
      const finalMallId = mallId || this.getCachedMallId()

      // 构建请求头
      const headers: Record<string, string> = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'cache-control': 'max-age=0',
        'origin': 'https://seller.kuajingmaihuo.com',
        'referer': 'https://seller.kuajingmaihuo.com/main/product/seller-select',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      }

      // 添加 anti-content 和 mallid
      if (antiContent) {
        headers['anti-content'] = antiContent
        console.info('[Temu Detector] 添加 anti-content 头部:', antiContent.substring(0, 50) + '...')
      } else {
        console.warn('[Temu Detector] 没有 anti-content，请求可能失败')
      }

      if (finalMallId) {
        headers['mallid'] = finalMallId
        console.info('[Temu Detector] 添加 mallid 头部:', finalMallId)
      } else {
        console.warn('[Temu Detector] 没有 mallId，请求可能失败')
      }

      let url: string
      let body: string

      if (apiType === 'todo') {
        // 获取待办事项数量
        url = 'https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount'
        body = JSON.stringify({})
      } else if (apiType === 'products') {
        // 获取商品列表
        url = 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier'
        const defaultParams = {
          pageSize: 50,
          pageNum: 1,
          supplierTodoTypeList: []
        }
        body = JSON.stringify({ ...defaultParams, ...params })
      } else {
        throw new Error(`不支持的 API 类型: ${apiType}`)
      }

      console.info('[Temu Detector] 发送请求:', { url, headers, body })

      const response = await fetch(url, {
        method: 'POST',
        headers,
        credentials: 'include',
        body
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[Temu Detector] API 返回数据:', data)

      return data
    } catch (error) {
      console.error('[Temu Detector] 获取 Temu 数据失败:', error)
      throw error
    }
  }
}

// 检查是否在 Temu 商家后台页面
const isTemuSellerPage = (): boolean => {
  const hostname = window.location.hostname
  const temuDomains = [
    'seller.temu.com',
    'seller.kuajingmaihuo.com',
    'seller-cn.temu.com',
    'agentseller.temu.com',
    'agentseller-us.temu.com'
  ]

  const isDomain = temuDomains.some(domain => hostname.includes(domain))
  const isPath = window.location.href.includes('/seller') ||
                 window.location.href.includes('/agentseller')

  console.info('[Temu Detector] 页面检查 - 域名匹配:', isDomain, '路径匹配:', isPath)
  return isDomain || isPath
}

// 只在 Temu 商家后台页面运行
if (isTemuSellerPage()) {
  console.info('[Temu Detector] 在 Temu 商家后台页面，启动检测器...')
  const detector = new TemuShopDetector()

  // 将检测器暴露到全局，供扩展程序调用
  ;(window as unknown as { temuShopDetector: TemuShopDetector }).temuShopDetector = detector

  // 监听来自 side panel 的消息
  chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    console.info('[Temu Detector] 收到消息:', request)

    if (request.action === 'GET_TEMU_INFO') {
      // 调用检测器获取原始 API 数据
      detector.getRawApiData()
        .then(result => {
          console.info('[Temu Detector] 获取原始 API 数据成功:', result)
          sendResponse({
            success: true,
            data: result
          })
        })
        .catch(error => {
          console.error('[Temu Detector] 获取原始 API 数据失败:', error)
          sendResponse({
            success: false,
            error: error.message || '获取失败'
          })
        })

      // 返回 true 表示异步响应
      return true
    }

    if (request.action === 'GET_ANTI_CONTENT') {
      // 获取 anti-content 头部
      const antiContent = detector.getAntiContentHeader()
      sendResponse({
        success: true,
        antiContent: antiContent
      })
      return true
    }

    if (request.action === 'DEBUG_ANTI_CONTENT') {
      // 调试 anti-content 获取过程
      console.info('[Temu Detector] 开始调试 anti-content 获取过程...')
      const antiContent = detector.getAntiContentHeader()
      sendResponse({
        success: true,
        antiContent: antiContent,
        debug: true
      })
      return true
    }

    if (request.action === 'REGISTER_INTERCEPTOR') {
      // 注册拦截器（实际上早期拦截器已经在document_start时注册了）
      console.info('[Temu Detector] 收到注册拦截器请求')

      // 检查早期拦截器是否已经工作
      const earlyInterceptor = (window as any).temuEarlyInterceptor
      if (earlyInterceptor) {
        const cache = earlyInterceptor.getCache()
        console.info('[Temu Detector] 早期拦截器已工作，当前缓存:', {
          antiContent: cache.antiContent ? cache.antiContent.substring(0, 50) + '...' : null,
          mallId: cache.mallId
        })

        sendResponse({
          success: true,
          message: '早期拦截器已注册并工作',
          cache: {
            antiContent: cache.antiContent ? cache.antiContent.substring(0, 50) + '...' : null,
            mallId: cache.mallId
          }
        })
      } else {
        console.warn('[Temu Detector] 早期拦截器未找到')
        sendResponse({
          success: false,
          message: '早期拦截器未找到，请检查扩展配置'
        })
      }
      return true
    }

    if (request.action === 'GET_TEMU_DATA') {
      // 获取 Temu 数据（商品列表、待办事项等）
      detector.getTemuData(request.apiType, request.params, request.mallId)
        .then(result => {
          console.info('[Temu Detector] 获取 Temu 数据成功:', result)
          sendResponse({
            success: true,
            data: result
          })
        })
        .catch(error => {
          console.error('[Temu Detector] 获取 Temu 数据失败:', error)
          sendResponse({
            success: false,
            error: error.message || '获取失败'
          })
        })

      // 返回 true 表示异步响应
      return true
    }

    // 其他消息类型
    return false
  })
} else {
  console.info('[Temu Detector] 不在 Temu 商家后台页面，跳过检测')
}

// 导出 onExecute 函数供 loader 调用
export function onExecute(context?: { perf?: { injectTime: number; loadTime: number } }) {
  console.info('[Temu Detector] onExecute 被调用，性能信息:', context?.perf)
  console.info('[Temu Detector] 当前页面 URL:', window.location.href)
  console.info('[Temu Detector] 当前页面 hostname:', window.location.hostname)

  // 添加一个明显的页面标记，方便调试
  const debugElement = document.createElement('div')
  debugElement.id = 'temu-detector-debug'
  debugElement.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #ff0000;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    z-index: 999999;
    font-size: 12px;
    font-family: monospace;
  `
  debugElement.textContent = 'Temu Detector Loaded'
  document.body?.appendChild(debugElement)

  // 5秒后移除调试元素
  setTimeout(() => {
    debugElement.remove()
  }, 5000)
}

export { TemuShopDetector }