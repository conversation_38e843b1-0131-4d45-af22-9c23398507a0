// 早期拦截器 - 完全复制ultimate-debug.js的成功逻辑
// 这个脚本会在 document_start 阶段运行，确保能拦截到所有网络请求

console.info('[Early Interceptor] 🚀 启动终极拦截器（复制ultimate-debug.js逻辑）...')

// 全局状态（完全复制ultimate-debug.js）
const ultimateDebugState = {
    interceptedRequests: [] as Array<{
        source: string
        url: string
        headers: Record<string, string>
        antiContent: string | null
        mallId: string | null
        timestamp: number
    }>,
    antiContentFound: null as string | null,
    mallIdFound: null as string | null
}

// 检查请求头的通用函数（完全复制ultimate-debug.js的逻辑）
function checkHeaders(headers: Record<string, string>, source: string, url: string) {
    console.info(`[Early Interceptor] 📋 检查${source}请求头:`, headers)

    // 检查anti-content的所有可能变体
    const antiContentKeys = [
        'anti-content', 'Anti-Content', 'ANTI-CONTENT', 'antiContent',
        'anti_content', 'Anti_Content', 'ANTI_CONTENT'
    ]

    let foundAntiContent = null
    let foundKey = null

    for (const key of antiContentKeys) {
        if (headers[key]) {
            foundAntiContent = headers[key]
            foundKey = key
            break
        }
    }

    if (foundAntiContent) {
        ultimateDebugState.antiContentFound = foundAntiContent
        console.info(`[Early Interceptor] 🎉 找到 anti-content (${source}, key: ${foundKey}):`, foundAntiContent)

        // 保存到localStorage（完全复制ultimate-debug.js的逻辑）
        try {
            localStorage.setItem('ultimate_anti_content', foundAntiContent)
            localStorage.setItem('ultimate_anti_content_source', source)
            if (foundKey) {
                localStorage.setItem('ultimate_anti_content_key', foundKey)
            }
            localStorage.setItem('ultimate_anti_content_time', Date.now().toString())
            localStorage.setItem('ultimate_anti_content_url', url)

            // 兼容扩展的键名
            localStorage.setItem('temu_cached_anti_content', foundAntiContent)
            localStorage.setItem('temu_anti_content_expiry', (Date.now() + 30 * 60 * 1000).toString())

            console.info('[Early Interceptor] ✅ 成功保存 anti-content 到localStorage')

            // 立即通知扩展程序
            try {
                window.postMessage({
                    type: 'TEMU_ANTI_CONTENT_FOUND',
                    antiContent: foundAntiContent,
                    source: source,
                    key: foundKey
                }, '*')
            } catch (e) {
                console.warn('[Early Interceptor] 发送消息失败:', e)
            }
        } catch (e) {
            console.warn('[Early Interceptor] 保存anti-content失败:', e)
        }
    }

    // 检查mallid的所有可能变体
    const mallIdKeys = ['mallid', 'mallId', 'MallId', 'MALLID', 'mall_id', 'Mall_Id']

    let foundMallId = null
    let foundMallKey = null

    for (const key of mallIdKeys) {
        if (headers[key]) {
            foundMallId = headers[key]
            foundMallKey = key
            break
        }
    }

    if (foundMallId) {
        ultimateDebugState.mallIdFound = foundMallId
        console.info(`[Early Interceptor] 🎉 找到 mallId (${source}, key: ${foundMallKey}):`, foundMallId)

        // 保存到localStorage
        try {
            localStorage.setItem('ultimate_mall_id', foundMallId)
            localStorage.setItem('ultimate_mall_id_source', source)
            if (foundMallKey) {
                localStorage.setItem('ultimate_mall_id_key', foundMallKey)
            }
            localStorage.setItem('ultimate_mall_id_time', Date.now().toString())

            // 兼容扩展的键名
            localStorage.setItem('temu_cached_mall_id', foundMallId)
            localStorage.setItem('temu_mall_id_expiry', (Date.now() + 60 * 60 * 1000).toString())

            console.info('[Early Interceptor] ✅ 成功保存 mallId 到localStorage')

            // 立即通知扩展程序
            try {
                window.postMessage({
                    type: 'TEMU_MALL_ID_FOUND',
                    mallId: foundMallId,
                    source: source,
                    key: foundMallKey
                }, '*')
            } catch (e) {
                console.warn('[Early Interceptor] 发送mallId消息失败:', e)
            }
        } catch (e) {
            console.warn('[Early Interceptor] 保存mallId失败:', e)
        }
    }

    // 记录请求（完全复制ultimate-debug.js）
    ultimateDebugState.interceptedRequests.push({
        source,
        url,
        headers,
        antiContent: foundAntiContent,
        mallId: foundMallId,
        timestamp: Date.now()
    })
}

// 从localStorage加载缓存
function loadCache() {
  try {
    // 加载anti-content（修复：使用正确的状态对象）
    const cachedAntiContent = localStorage.getItem('temu_cached_anti_content')
    const antiContentExpiry = localStorage.getItem('temu_anti_content_expiry')
    
    if (cachedAntiContent && antiContentExpiry) {
      const expiry = parseInt(antiContentExpiry)
      if (Date.now() < expiry) {
        ultimateDebugState.antiContentFound = cachedAntiContent
        console.info('[Early Interceptor] 从缓存加载 anti-content:', cachedAntiContent.substring(0, 50) + '...')
      } else {
        localStorage.removeItem('temu_cached_anti_content')
        localStorage.removeItem('temu_anti_content_expiry')
      }
    }

    // 也检查 ultimate-debug.js 保存的缓存
    const ultimateAntiContent = localStorage.getItem('ultimate_anti_content')
    if (ultimateAntiContent && !ultimateDebugState.antiContentFound) {
      ultimateDebugState.antiContentFound = ultimateAntiContent
      console.info('[Early Interceptor] 从ultimate-debug缓存加载 anti-content:', ultimateAntiContent.substring(0, 50) + '...')
    }

    // 加载mallId
    const cachedMallId = localStorage.getItem('temu_cached_mall_id')
    const mallIdExpiry = localStorage.getItem('temu_mall_id_expiry')
    
    if (cachedMallId && mallIdExpiry) {
      const expiry = parseInt(mallIdExpiry)
      if (Date.now() < expiry) {
        ultimateDebugState.mallIdFound = cachedMallId
        console.info('[Early Interceptor] 从缓存加载 mallId:', cachedMallId)
      } else {
        localStorage.removeItem('temu_cached_mall_id')
        localStorage.removeItem('temu_mall_id_expiry')
      }
    }

    // 也检查 ultimate-debug.js 保存的 mallId
    const ultimateMallId = localStorage.getItem('ultimate_mall_id')
    if (ultimateMallId && !ultimateDebugState.mallIdFound) {
      ultimateDebugState.mallIdFound = ultimateMallId
      console.info('[Early Interceptor] 从ultimate-debug缓存加载 mallId:', ultimateMallId)
    }
  } catch (error) {
    console.warn('[Early Interceptor] 加载缓存失败:', error)
  }
}

// 设置fetch拦截（完全复制ultimate-debug.js的成功逻辑）
function setupFetchInterception() {
  const originalFetch = window.fetch

  window.fetch = function(...args) {
    const [url, options] = args
    const urlString = typeof url === 'string' ? url : url.toString()

    if (urlString.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🎯 拦截fetch请求:', urlString)
      console.info('[Early Interceptor] 📋 请求选项:', options)

      if (options && options.headers) {
        checkHeaders(options.headers as Record<string, string>, 'fetch', urlString)
      }
    }

    return originalFetch.apply(this, args)
  }

  console.info('[Early Interceptor] ✅ Fetch拦截器已设置')
}

// 设置 jQuery 拦截
function setupJQueryInterception() {
  // 延迟执行，等待 jQuery 加载
  setTimeout(() => {
    if (typeof (window as any).$ !== 'undefined' && (window as any).$.ajax) {
      const $ = (window as any).$
      const originalAjax = $.ajax

      $.ajax = function(options: any) {
        if (options && options.url && options.url.includes('seller.kuajingmaihuo.com')) {
          console.info('[Early Interceptor] 🎯 拦截jQuery请求:', options.url)
          console.info('[Early Interceptor] 📋 jQuery选项:', options)

          if (options.headers) {
            checkHeaders(options.headers, 'jQuery', options.url)
          }
        }

        return originalAjax.apply(this, arguments)
      }

      console.info('[Early Interceptor] ✅ jQuery拦截器已设置')
    }
  }, 1000)

  // 也尝试立即设置
  try {
    if (typeof (window as any).$ !== 'undefined' && (window as any).$.ajax) {
      const $ = (window as any).$
      const originalAjax = $.ajax

      $.ajax = function(options: any) {
        if (options && options.url && options.url.includes('seller.kuajingmaihuo.com')) {
          console.info('[Early Interceptor] 🎯 拦截jQuery请求:', options.url)
          console.info('[Early Interceptor] 📋 jQuery选项:', options)

          if (options.headers) {
            checkHeaders(options.headers, 'jQuery', options.url)
          }
        }

        return originalAjax.apply(this, arguments)
      }

      console.info('[Early Interceptor] ✅ jQuery拦截器已设置（立即）')
    }
  } catch (e) {
    console.info('[Early Interceptor] jQuery暂未加载，将稍后尝试')
  }
}

// 设置 axios 拦截
function setupAxiosInterception() {
  // 延迟执行，等待 axios 加载
  setTimeout(() => {
    if (typeof (window as any).axios !== 'undefined') {
      const axios = (window as any).axios

      axios.interceptors.request.use(function (config: any) {
        if (config.url && config.url.includes('seller.kuajingmaihuo.com')) {
          console.info('[Early Interceptor] 🎯 拦截axios请求:', config.url)
          console.info('[Early Interceptor] 📋 axios配置:', config)

          if (config.headers) {
            checkHeaders(config.headers, 'axios', config.url)
          }
        }
        return config
      })

      console.info('[Early Interceptor] ✅ Axios拦截器已设置')
    }
  }, 1000)

  // 也尝试立即设置
  try {
    if (typeof (window as any).axios !== 'undefined') {
      const axios = (window as any).axios

      axios.interceptors.request.use(function (config: any) {
        if (config.url && config.url.includes('seller.kuajingmaihuo.com')) {
          console.info('[Early Interceptor] 🎯 拦截axios请求:', config.url)
          console.info('[Early Interceptor] 📋 axios配置:', config)

          if (config.headers) {
            checkHeaders(config.headers, 'axios', config.url)
          }
        }
        return config
      })

      console.info('[Early Interceptor] ✅ Axios拦截器已设置（立即）')
    }
  } catch (e) {
    console.info('[Early Interceptor] Axios暂未加载，将稍后尝试')
  }
}



// 检查单个请求头（完全复制ultimate-debug.js的逻辑）
function checkSingleHeader(name: string, value: string, source: string, url: string) {
  const lowerName = name.toLowerCase()

  if (lowerName.includes('anti') && lowerName.includes('content')) {
    ultimateDebugState.antiContentFound = value
    console.info(`[Early Interceptor] 🎉 找到 anti-content (${source}, header: ${name}):`, value)

    try {
      localStorage.setItem('ultimate_anti_content', value)
      localStorage.setItem('ultimate_anti_content_source', source)
      localStorage.setItem('ultimate_anti_content_key', name)
      localStorage.setItem('ultimate_anti_content_time', Date.now().toString())
      localStorage.setItem('ultimate_anti_content_url', url)
      localStorage.setItem('temu_cached_anti_content', value)
      localStorage.setItem('temu_anti_content_expiry', (Date.now() + 30 * 60 * 1000).toString())
    } catch (e) {
      console.warn('[Early Interceptor] 保存anti-content失败:', e)
    }
  }

  if (lowerName.includes('mall') && lowerName.includes('id')) {
    ultimateDebugState.mallIdFound = value
    console.info(`[Early Interceptor] 🎉 找到 mallId (${source}, header: ${name}):`, value)

    try {
      localStorage.setItem('ultimate_mall_id', value)
      localStorage.setItem('ultimate_mall_id_source', source)
      localStorage.setItem('ultimate_mall_id_key', name)
      localStorage.setItem('ultimate_mall_id_time', Date.now().toString())
      localStorage.setItem('temu_cached_mall_id', value)
      localStorage.setItem('temu_mall_id_expiry', (Date.now() + 60 * 60 * 1000).toString())
    } catch (e) {
      console.warn('[Early Interceptor] 保存mallId失败:', e)
    }
  }
}

// 设置XMLHttpRequest拦截
function setupXHRInterception() {
  const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader
  const originalOpen = XMLHttpRequest.prototype.open
  const originalSend = XMLHttpRequest.prototype.send

  // 存储XHR实例信息
  const xhrInstances = new WeakMap<XMLHttpRequest, { url: string; headers: Record<string, string> }>()

  XMLHttpRequest.prototype.open = function(method: string, url: string | URL, async?: boolean, username?: string | null, password?: string | null) {
    const urlString = url.toString()
    xhrInstances.set(this, { url: urlString, headers: {} })

    if (urlString.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🌐 XHR open:', method, urlString)
    }

    return originalOpen.call(this, method, url, async ?? true, username, password)
  }

  XMLHttpRequest.prototype.setRequestHeader = function(name: string, value: string) {
    const instance = xhrInstances.get(this)
    if (instance) {
      instance.headers[name] = value

      if (instance.url.includes('seller.kuajingmaihuo.com')) {
        console.info('[Early Interceptor] 🎯 XHR设置头部:', name, '=', value.substring(0, 100) + (value.length > 100 ? '...' : ''))
        checkSingleHeader(name, value, 'XHR', instance.url)
      }
    }

    return originalSetRequestHeader.call(this, name, value)
  }

  XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null) {
    const instance = xhrInstances.get(this)
    if (instance && instance.url.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🎯 XHR发送请求:', instance.url)
      console.info('[Early Interceptor] 📋 所有头部:', instance.headers)
      checkHeaders(instance.headers, 'XHR', instance.url)
    }

    return originalSend.call(this, body)
  }

  console.info('[Early Interceptor] ✅ XHR拦截器已设置')
}

// 暴露全局访问接口（复制ultimate-debug.js的接口）
function exposeGlobalInterface() {
  interface TemuEarlyInterceptor {
    getCache: () => typeof ultimateDebugState
    getCachedAntiContent: () => string | null
    getCachedMallId: () => string | null
    getState: () => typeof ultimateDebugState
    report: () => typeof ultimateDebugState
    clearCache: () => void
  }

  // 直接赋值到window对象，避免类型错误
  ;(window as unknown as Record<string, unknown>).temuEarlyInterceptor = {
    getCache: () => ultimateDebugState,
    getCachedAntiContent: () => {
      return ultimateDebugState.antiContentFound
    },
    getCachedMallId: () => {
      return ultimateDebugState.mallIdFound
    },
    getState: () => ultimateDebugState,
    report: () => {
      console.info('📊 Early Interceptor 报告:', {
        timestamp: new Date().toLocaleString(),
        interceptedRequests: ultimateDebugState.interceptedRequests.length,
        antiContentFound: ultimateDebugState.antiContentFound,
        mallIdFound: ultimateDebugState.mallIdFound
      })
      return ultimateDebugState
    },
    clearCache: () => {
      ultimateDebugState.antiContentFound = null
      ultimateDebugState.mallIdFound = null
      ultimateDebugState.interceptedRequests = []
      localStorage.removeItem('ultimate_anti_content')
      localStorage.removeItem('ultimate_mall_id')
      localStorage.removeItem('temu_cached_anti_content')
      localStorage.removeItem('temu_cached_mall_id')
      console.info('[Early Interceptor] 🗑️ 缓存已清除')
    }
  } as TemuEarlyInterceptor

  console.info('[Early Interceptor] ✅ 全局接口已暴露: window.temuEarlyInterceptor')
}

// 初始化
function init() {
  console.info('[Early Interceptor] 🚀 开始初始化...')
  
  // 加载现有缓存
  loadCache()
  
  // 设置所有拦截器
  setupFetchInterception()
  setupXHRInterception()
  setupJQueryInterception()
  setupAxiosInterception()
  
  // 暴露全局接口
  exposeGlobalInterface()
  
  // 添加定期重试机制，确保第三方库拦截器被正确设置
  let retryCount = 0
  const maxRetries = 10
  const retryInterval = setInterval(() => {
    retryCount++
    
    // 重试设置 jQuery 拦截器
    if (typeof (window as unknown as { $?: unknown }).$ !== 'undefined') {
      setupJQueryInterception()
    }
    
    // 重试设置 axios 拦截器
    if (typeof (window as unknown as { axios?: unknown }).axios !== 'undefined') {
      setupAxiosInterception()
    }
    
    if (retryCount >= maxRetries) {
      clearInterval(retryInterval)
      console.info('[Early Interceptor] 🔄 重试设置拦截器完成')
    }
  }, 2000)
  
  console.info('[Early Interceptor] ✅ 初始化完成')
  console.info('[Early Interceptor] 💾 当前状态:', {
    antiContent: ultimateDebugState.antiContentFound ? ultimateDebugState.antiContentFound.substring(0, 50) + '...' : null,
    mallId: ultimateDebugState.mallIdFound,
    interceptedRequests: ultimateDebugState.interceptedRequests.length
  })
}

// 立即执行初始化，使用 try-catch 包装
try {
  console.info('[Early Interceptor] 🚀 脚本开始执行...')
  init()
} catch (error) {
  console.error('[Early Interceptor] ❌ 初始化失败:', error)
  
  // 即使初始化失败，也尝试设置基本的fetch拦截
  try {
    console.info('[Early Interceptor] 🔧 尝试设置基本拦截器...')
    const originalFetch = window.fetch
    window.fetch = function(...args) {
      const [url, options] = args
      const urlString = typeof url === 'string' ? url : url.toString()

      if (urlString.includes('seller.kuajingmaihuo.com')) {
        console.info('[Early Interceptor] 🎯 基本拦截fetch请求:', urlString)
        console.info('[Early Interceptor] 📋 基本拦截请求选项:', options)
      }

      return originalFetch.apply(this, args)
    }
    
    // 设置简单的全局接口
    ;(window as unknown as Record<string, unknown>).temuEarlyInterceptor = {
      getCachedAntiContent: () => localStorage.getItem('temu_cached_anti_content'),
      getCachedMallId: () => localStorage.getItem('temu_cached_mall_id'),
      getState: () => ({
        antiContentFound: localStorage.getItem('temu_cached_anti_content'),
        mallIdFound: localStorage.getItem('temu_cached_mall_id'),
        interceptedRequests: []
      }),
      report: () => console.info('[Early Interceptor] 基本报告')
    }
    
    console.info('[Early Interceptor] ✅ 基本拦截器设置完成')
  } catch (fallbackError) {
    console.error('[Early Interceptor] ❌ 基本拦截器设置也失败:', fallbackError)
  }
}

// 额外的安全检查
setTimeout(() => {
  if (!(window as unknown as Record<string, unknown>).temuEarlyInterceptor) {
    console.warn('[Early Interceptor] ⚠️ 全局接口未设置成功，重试...')
    try {
      ;(window as unknown as Record<string, unknown>).temuEarlyInterceptor = {
        getCachedAntiContent: () => localStorage.getItem('temu_cached_anti_content'),
        getCachedMallId: () => localStorage.getItem('temu_cached_mall_id'),
        getState: () => ({
          antiContentFound: localStorage.getItem('temu_cached_anti_content'),
          mallIdFound: localStorage.getItem('temu_cached_mall_id'),
          interceptedRequests: []
        }),
        report: () => console.info('[Early Interceptor] 延迟报告')
      }
      console.info('[Early Interceptor] ✅ 延迟设置全局接口成功')
    } catch (retryError) {
      console.error('[Early Interceptor] ❌ 延迟设置也失败:', retryError)
    }
  } else {
    console.info('[Early Interceptor] ✅ 全局接口检查通过')
  }
}, 1000)
