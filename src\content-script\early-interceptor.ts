// 早期请求拦截器 - 在页面加载最早阶段设置
// 这个文件会在其他脚本之前加载，确保能捕获所有请求

console.info('[Early Interceptor] 开始设置早期请求拦截器...')

// 全局缓存对象
interface CacheData {
  antiContent: string | null
  antiContentExpiry: number
  mallId: string | null
  mallIdExpiry: number
}

const globalCache: CacheData = {
  antiContent: null,
  antiContentExpiry: 0,
  mallId: null,
  mallIdExpiry: 0
}

// 缓存anti-content
function cacheAntiContent(antiContent: string) {
  if (antiContent && antiContent !== globalCache.antiContent) {
    globalCache.antiContent = antiContent
    globalCache.antiContentExpiry = Date.now() + 30 * 60 * 1000 // 30分钟过期
    
    // 保存到localStorage
    try {
      localStorage.setItem('temu_cached_anti_content', antiContent)
      localStorage.setItem('temu_anti_content_expiry', globalCache.antiContentExpiry.toString())
      console.info('[Early Interceptor] 🎯 缓存 anti-content:', antiContent.substring(0, 50) + '...')
    } catch (error) {
      console.warn('[Early Interceptor] 保存 anti-content 到 localStorage 失败:', error)
    }
  }
}

// 缓存mallId
function cacheMallId(mallId: string) {
  if (mallId && mallId !== globalCache.mallId) {
    globalCache.mallId = mallId
    globalCache.mallIdExpiry = Date.now() + 60 * 60 * 1000 // 1小时过期
    
    // 保存到localStorage
    try {
      localStorage.setItem('temu_cached_mall_id', mallId)
      localStorage.setItem('temu_mall_id_expiry', globalCache.mallIdExpiry.toString())
      console.info('[Early Interceptor] 🎯 缓存 mallId:', mallId)
    } catch (error) {
      console.warn('[Early Interceptor] 保存 mallId 到 localStorage 失败:', error)
    }
  }
}

// 从localStorage加载缓存
function loadCache() {
  try {
    // 加载anti-content
    const cachedAntiContent = localStorage.getItem('temu_cached_anti_content')
    const antiContentExpiry = localStorage.getItem('temu_anti_content_expiry')
    
    if (cachedAntiContent && antiContentExpiry) {
      const expiry = parseInt(antiContentExpiry)
      if (Date.now() < expiry) {
        globalCache.antiContent = cachedAntiContent
        globalCache.antiContentExpiry = expiry
        console.info('[Early Interceptor] 从缓存加载 anti-content:', cachedAntiContent.substring(0, 50) + '...')
      } else {
        localStorage.removeItem('temu_cached_anti_content')
        localStorage.removeItem('temu_anti_content_expiry')
      }
    }

    // 加载mallId
    const cachedMallId = localStorage.getItem('temu_cached_mall_id')
    const mallIdExpiry = localStorage.getItem('temu_mall_id_expiry')
    
    if (cachedMallId && mallIdExpiry) {
      const expiry = parseInt(mallIdExpiry)
      if (Date.now() < expiry) {
        globalCache.mallId = cachedMallId
        globalCache.mallIdExpiry = expiry
        console.info('[Early Interceptor] 从缓存加载 mallId:', cachedMallId)
      } else {
        localStorage.removeItem('temu_cached_mall_id')
        localStorage.removeItem('temu_mall_id_expiry')
      }
    }
  } catch (error) {
    console.warn('[Early Interceptor] 加载缓存失败:', error)
  }
}

// 设置fetch拦截
function setupFetchInterception() {
  const originalFetch = window.fetch
  
  window.fetch = async function(...args) {
    const [url, options] = args
    
    // 检查是否是Temu API请求
    if (typeof url === 'string' && url.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🌐 拦截fetch请求:', url)
      
      if (options && options.headers) {
        console.info('[Early Interceptor] 📋 请求头:', options.headers)

        // 检查并缓存关键头部
        const headers = options.headers as Record<string, string>

        // 检查anti-content（基于成功的拦截结果，主要是小写形式）
        if (headers['anti-content']) {
          console.info('[Early Interceptor] 🎯 发现 anti-content:', headers['anti-content'].substring(0, 50) + '...')
          cacheAntiContent(headers['anti-content'])
        }

        // 检查mallid（基于成功的拦截结果，主要是小写形式）
        if (headers['mallid']) {
          console.info('[Early Interceptor] 🎯 发现 mallid:', headers['mallid'])
          cacheMallId(headers['mallid'])
        }
      }
    }
    
    return originalFetch.apply(this, args)
  }
  
  console.info('[Early Interceptor] ✅ Fetch拦截器已设置')
}

// 设置XMLHttpRequest拦截
function setupXHRInterception() {
  const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader
  const originalOpen = XMLHttpRequest.prototype.open
  const originalSend = XMLHttpRequest.prototype.send

  // 存储XHR实例信息
  const xhrInstances = new WeakMap<XMLHttpRequest, { url: string; headers: Record<string, string> }>()

  XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
    const urlString = url.toString()
    xhrInstances.set(this, { url: urlString, headers: {} })

    if (urlString.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🌐 XHR open:', method, urlString)
    }

    return originalOpen.apply(this, [method, url, ...args])
  }

  XMLHttpRequest.prototype.setRequestHeader = function(name: string, value: string) {
    const instance = xhrInstances.get(this)
    if (instance) {
      instance.headers[name] = value

      // 如果是Temu请求，检查关键头部
      if (instance.url.includes('seller.kuajingmaihuo.com')) {
        console.info('[Early Interceptor] 📋 XHR设置头部:', name, '=', value.substring(0, 50) + (value.length > 50 ? '...' : ''))

        // 检查anti-content（精确匹配）
        if (name === 'anti-content') {
          console.info('[Early Interceptor] 🎯 XHR捕获 anti-content:', value.substring(0, 50) + '...')
          cacheAntiContent(value)
        } else if (name === 'mallid') {
          console.info('[Early Interceptor] 🎯 XHR捕获 mallid:', value)
          cacheMallId(value)
        }
      }
    }

    return originalSetRequestHeader.call(this, name, value)
  }

  XMLHttpRequest.prototype.send = function(body?: any) {
    const instance = xhrInstances.get(this)
    if (instance && instance.url.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🚀 XHR发送请求:', instance.url)
      console.info('[Early Interceptor] 📋 所有头部:', instance.headers)

      // 最后检查一次关键头部
      if (instance.headers['anti-content']) {
        console.info('[Early Interceptor] 🎯 在发送时确认 anti-content')
        cacheAntiContent(instance.headers['anti-content'])
      }
      if (instance.headers['mallid']) {
        console.info('[Early Interceptor] 🎯 在发送时确认 mallid')
        cacheMallId(instance.headers['mallid'])
      }
    }

    return originalSend.call(this, body)
  }

  console.info('[Early Interceptor] ✅ XHR拦截器已设置')
}

// 暴露全局访问接口
function exposeGlobalInterface() {
  ;(window as any).temuEarlyInterceptor = {
    getCache: () => globalCache,
    getCachedAntiContent: () => {
      if (globalCache.antiContent && Date.now() < globalCache.antiContentExpiry) {
        return globalCache.antiContent
      }
      return null
    },
    getCachedMallId: () => {
      if (globalCache.mallId && Date.now() < globalCache.mallIdExpiry) {
        return globalCache.mallId
      }
      return null
    },
    clearCache: () => {
      globalCache.antiContent = null
      globalCache.antiContentExpiry = 0
      globalCache.mallId = null
      globalCache.mallIdExpiry = 0
      localStorage.removeItem('temu_cached_anti_content')
      localStorage.removeItem('temu_anti_content_expiry')
      localStorage.removeItem('temu_cached_mall_id')
      localStorage.removeItem('temu_mall_id_expiry')
      console.info('[Early Interceptor] 🗑️ 缓存已清除')
    }
  }
  
  console.info('[Early Interceptor] ✅ 全局接口已暴露: window.temuEarlyInterceptor')
}

// 初始化
function init() {
  console.info('[Early Interceptor] 🚀 开始初始化...')
  
  // 加载现有缓存
  loadCache()
  
  // 设置拦截器
  setupFetchInterception()
  setupXHRInterception()
  
  // 暴露全局接口
  exposeGlobalInterface()
  
  console.info('[Early Interceptor] ✅ 初始化完成')
  console.info('[Early Interceptor] 💾 当前缓存状态:', {
    antiContent: globalCache.antiContent ? globalCache.antiContent.substring(0, 50) + '...' : null,
    mallId: globalCache.mallId
  })
}

// 立即执行初始化
init()

export { globalCache, cacheAntiContent, cacheMallId }
