import amazonDataService from '../services/amazonDataService'
import configStorageService from '../services/configStorageService'

// 采集状态管理
let isCollecting = false
let collectedCount = 0

// 创建批量采集按钮（页面右侧中间偏上）
function createBatchCollectionButton(): HTMLElement {
  const button = document.createElement('button')
  button.className = 'amazon-batch-collect-btn'
  button.type = 'button'

  // 获取匹配的商品数量
  const productCount = getMatchedProductCount()

  button.style.cssText = `
    position: fixed;
    top: 35vh;
    right: 20px;
    z-index: 9999;
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    background: #ff4d4f;
    color: white;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
  `

  // 创建图标
  const icon = document.createElement('div')
  icon.innerHTML = `
    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
      <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
    </svg>
  `
  button.appendChild(icon)

  // 创建文字和数量
  const textContainer = document.createElement('div')
  textContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
  `

  const mainText = document.createElement('span')
  mainText.textContent = '[HJ]批量采集'
  mainText.style.fontSize = '14px'

  const countText = document.createElement('span')
  countText.className = 'product-count'
  countText.textContent = `${productCount} 个商品`
  countText.style.cssText = `
    font-size: 12px;
    opacity: 0.9;
    font-weight: normal;
  `

  textContainer.appendChild(mainText)
  textContainer.appendChild(countText)
  button.appendChild(textContainer)

  // 添加点击事件
  button.addEventListener('click', handleCollectionClick)

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.transform = 'scale(1.05)'
    button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.3)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.transform = 'scale(1)'
    button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)'
  })

  return button
}

// 获取匹配的商品数量
function getMatchedProductCount(): number {
  const productElements = document.querySelectorAll('[data-asin]:not(.amazon-collected)')
  return productElements.length
}

// 创建单品采集按钮（固定显示在商品图片右上角）
function createProductCollectionButton(productElement: Element): HTMLElement {
  const button = document.createElement('div')
  button.className = 'amazon-product-collect-btn'
  button.style.cssText = `
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    z-index: 10;
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
  `
  button.textContent = '[HJ]单品采集'

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.background = '#ff7875'
    button.style.transform = 'scale(1.05)'
    button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.4)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.background = '#ff4d4f'
    button.style.transform = 'scale(1)'
    button.style.boxShadow = '0 2px 6px rgba(255, 77, 79, 0.3)'
  })

  // 添加点击事件
  button.addEventListener('click', (e) => {
    e.preventDefault()
    e.stopPropagation()
    handleSingleProductCollection(productElement)
  })

  return button
}

// 处理采集按钮点击
async function handleCollectionClick() {
  if (isCollecting) {
    showNotification('正在采集中，请稍候...', 'warning')
    return
  }

  try {
    // 检查配置完整性
    const configCheck = await configStorageService.isConfigComplete()
    if (!configCheck.isComplete) {
      showNotification(`配置不完整，缺少：${configCheck.missingFields.join(', ')}`, 'error')
      return
    }

    if (isAmazonSearchPage()) {
      await handleBatchCollection()
    } else if (isAmazonProductPage()) {
      await handleSingleProductCollection()
    } else {
      showNotification('当前页面不支持采集', 'warning')
    }
  } catch (error) {
    console.error('[AmazonCollector] 采集失败:', error)
    showNotification('采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  }
}

// 处理单品采集
async function handleSingleProductCollection(productElement?: Element) {
  try {
    isCollecting = true
    updateButtonState()

    showNotification('开始采集商品数据...', 'info')

    // 构造Amazon产品链接
    let productUrl = ''

    if (productElement) {
      // 从搜索页面的商品卡片中提取ASIN
      const asin = productElement.getAttribute('data-asin')
      if (asin) {
        productUrl = `https://www.amazon.com/dp/${asin}`
        console.info('[AmazonCollector] 从商品卡片构造产品链接:', productUrl)
      } else {
        throw new Error('无法从商品卡片获取ASIN')
      }
    } else {
      // 从当前页面URL中提取
      const currentUrl = window.location.href
      if (currentUrl.includes('/dp/')) {
        const asinMatch = currentUrl.match(/\/dp\/([A-Z0-9]{10})/)
        if (asinMatch) {
          productUrl = `https://www.amazon.com/dp/${asinMatch[1]}`
          console.info('[AmazonCollector] 从当前页面构造产品链接:', productUrl)
        } else {
          throw new Error('无法从当前页面URL提取ASIN')
        }
      } else {
        throw new Error('当前页面不是Amazon产品页面')
      }
    }

    // 发送消息到background.js进行数据提取和组装
    console.info('[AmazonCollector] 发送产品链接到background.js:', productUrl)

    const result = await chrome.runtime.sendMessage({
      action: 'EXTRACT_AND_ASSEMBLE_AMAZON_DATA',
      productUrl: productUrl
    })

    if (!result.success) {
      throw new Error(result.error || '数据处理失败')
    }

    console.info('[AmazonCollector] 收到background.js处理结果:', result)

    // 显示数据预览弹窗
    showDataPreviewModal(result.amazonData, result.dianxiaomiData)

    collectedCount++
    updateProductCount()

    showNotification(`商品数据采集完成，请查看预览窗口`, 'success')

    // 如果是从产品列表中采集，标记该产品已采集
    if (productElement) {
      markProductAsCollected(productElement)
    }

  } catch (error) {
    console.error('[AmazonCollector] 单品采集失败:', error)
    showNotification('单品采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 处理批量采集
async function handleBatchCollection() {
  try {
    isCollecting = true
    updateButtonState()

    showNotification('开始批量采集...', 'info')

    // 获取当前页面的所有产品
    const productElements = document.querySelectorAll('[data-asin]:not(.amazon-collected)')
    
    if (productElements.length === 0) {
      showNotification('当前页面没有找到可采集的商品', 'warning')
      return
    }

    let successCount = 0
    let failCount = 0

    for (let i = 0; i < productElements.length; i++) {
      const productElement = productElements[i]
      const asin = productElement.getAttribute('data-asin')
      
      if (!asin) continue

      try {
        showNotification(`正在采集第 ${i + 1}/${productElements.length} 个商品...`, 'info')
        
        // 模拟点击产品链接获取详情
        const productLink = productElement.querySelector('h3 a, .s-link-style a') as HTMLAnchorElement
        if (productLink) {
          // 这里可以实现在新标签页中打开产品页面进行采集
          // 暂时跳过，直接标记为已采集
          markProductAsCollected(productElement)
          successCount++
          collectedCount++
        }

        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error(`[AmazonCollector] 采集商品 ${asin} 失败:`, error)
        failCount++
      }
    }

    updateProductCount()
    showNotification(`批量采集完成！成功: ${successCount}, 失败: ${failCount}`, 'success')

    // 检查是否有下一页
    if (hasNextPage()) {
      const shouldContinue = confirm('当前页面采集完成，是否继续采集下一页？')
      if (shouldContinue) {
        goToNextPage()
      }
    }

  } catch (error) {
    console.error('[AmazonCollector] 批量采集失败:', error)
    showNotification('批量采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 标记产品为已采集
function markProductAsCollected(productElement: Element) {
  productElement.classList.add('amazon-collected')
  productElement.setAttribute('style', 'opacity: 0.6; border: 2px solid #52c41a;')
  
  // 添加已采集标记
  const badge = document.createElement('div')
  badge.style.cssText = `
    position: absolute;
    top: 8px;
    right: 8px;
    background: #52c41a;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    z-index: 10;
  `
  badge.textContent = '已采集'
  
  const container = productElement.querySelector('[data-cy="title-recipe-container"], .s-result-item')
  if (container) {
    container.style.position = 'relative'
    container.appendChild(badge)
  }
}

// 更新按钮状态
function updateButtonState() {
  const button = document.querySelector('.amazon-batch-collect-btn') as HTMLElement
  if (button) {
    if (isCollecting) {
      button.style.background = '#faad14'
      button.style.cursor = 'not-allowed'
      const mainText = button.querySelector('span:first-child') as HTMLElement
      if (mainText) {
        mainText.textContent = '采集中...'
      }
    } else {
      button.style.background = '#ff4d4f'
      button.style.cursor = 'pointer'
      const mainText = button.querySelector('span:first-child') as HTMLElement
      if (mainText) {
        mainText.textContent = '批量采集'
      }
    }
  }
}

// 更新商品数量显示
function updateProductCount() {
  const countText = document.querySelector('.product-count') as HTMLElement
  if (countText) {
    const productCount = getMatchedProductCount()
    countText.textContent = `${productCount} 个商品`
  }
}

// 显示数据预览弹窗
function showDataPreviewModal(amazonData: Record<string, any>, dianxiaomiData: Record<string, any>) {
  // 移除已存在的弹窗
  const existingModal = document.querySelector('.amazon-data-preview-modal')
  if (existingModal) {
    existingModal.remove()
  }

  // 创建弹窗容器
  const modal = document.createElement('div')
  modal.className = 'amazon-data-preview-modal'
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    font-family: Arial, sans-serif;
  `

  // 创建弹窗内容
  const modalContent = document.createElement('div')
  modalContent.style.cssText = `
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 1000px;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `

  // 创建弹窗HTML内容
  modalContent.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
      <h3 style="margin: 0; color: #333;">Amazon商品数据预览</h3>
      <button class="close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999; padding: 0; width: 30px; height: 30px;">×</button>
    </div>

    <div style="display: flex; border-bottom: 1px solid #eee;">
      <button class="tab-btn active" data-tab="preview" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid #ff4d4f; color: #ff4d4f;">数据预览</button>
      <button class="tab-btn" data-tab="raw" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent;">原始数据</button>
      <button class="tab-btn" data-tab="formatted" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent;">组装数据</button>
    </div>

    <div style="flex: 1; overflow: auto; padding: 20px;">
      <div class="tab-content" id="preview-tab" style="display: block;">
        <h4 style="margin: 0 0 15px 0; color: #333;">数据预览</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
          <div><label style="font-weight: 500; color: #666; display: block;">商品名称:</label><span>${amazonData.title || '未获取'}</span></div>
          <div><label style="font-weight: 500; color: #666; display: block;">ASIN:</label><span>${amazonData.asin || '未获取'}</span></div>
          <div><label style="font-weight: 500; color: #666; display: block;">价格:</label><span>$${amazonData.price || '未获取'}</span></div>
          <div><label style="font-weight: 500; color: #666; display: block;">评分:</label><span>${amazonData.rating || '未获取'} (${amazonData.reviewCount || 0} 评论)</span></div>
          <div><label style="font-weight: 500; color: #666; display: block;">分类ID:</label><span>${dianxiaomiData.categoryId || '未设置'}</span></div>
          <div><label style="font-weight: 500; color: #666; display: block;">店铺ID:</label><span>${dianxiaomiData.shopId || '未设置'}</span></div>
          <div><label style="font-weight: 500; color: #666; display: block;">图片数量:</label><span>${amazonData.imageUrls?.length || 0} 张</span></div>
          <div><label style="font-weight: 500; color: #666; display: block;">变体数量:</label><span>${amazonData.variations?.length || 0} 个</span></div>
        </div>
      </div>

      <div class="tab-content" id="raw-tab" style="display: none;">
        <h4 style="margin: 0 0 15px 0; color: #333;">Amazon原始数据</h4>
        <pre style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; overflow: auto; max-height: 400px; white-space: pre-wrap; word-break: break-all;">${JSON.stringify(amazonData, null, 2)}</pre>
      </div>

      <div class="tab-content" id="formatted-tab" style="display: none;">
        <h4 style="margin: 0 0 15px 0; color: #333;">店小秘格式数据</h4>
        <pre style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; overflow: auto; max-height: 400px; white-space: pre-wrap; word-break: break-all;">${JSON.stringify(dianxiaomiData, null, 2)}</pre>
      </div>
    </div>

    <div style="display: flex; justify-content: flex-end; gap: 10px; padding: 20px; border-top: 1px solid #eee;">
      <button class="btn-close" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #6c757d; color: white;">关闭</button>
      <button class="btn-copy" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white;">复制JSON</button>
      <button class="btn-download" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #28a745; color: white;">下载JSON</button>
    </div>
  `

  modal.appendChild(modalContent)
  document.body.appendChild(modal)

  // 添加事件监听器
  setupModalEventListeners(modal, amazonData, dianxiaomiData)
}

// 设置弹窗事件监听器
function setupModalEventListeners(modal: HTMLElement, amazonData: Record<string, any>, dianxiaomiData: Record<string, any>) {
  // 关闭按钮
  const closeBtn = modal.querySelector('.close-btn')
  const btnClose = modal.querySelector('.btn-close')

  const closeModal = () => modal.remove()

  closeBtn?.addEventListener('click', closeModal)
  btnClose?.addEventListener('click', closeModal)

  // 点击背景关闭
  modal.addEventListener('click', (e) => {
    if (e.target === modal) closeModal()
  })

  // 标签页切换
  const tabBtns = modal.querySelectorAll('.tab-btn')
  const tabContents = modal.querySelectorAll('.tab-content')

  tabBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      const tabName = btn.getAttribute('data-tab')

      // 更新按钮状态
      tabBtns.forEach(b => {
        const element = b as HTMLElement
        element.style.borderBottomColor = 'transparent'
        element.style.color = '#333'
      })
      const btnElement = btn as HTMLElement
      btnElement.style.borderBottomColor = '#ff4d4f'
      btnElement.style.color = '#ff4d4f'

      // 显示对应内容
      tabContents.forEach(content => {
        const element = content as HTMLElement
        element.style.display = 'none'
      })
      const targetContent = modal.querySelector(`#${tabName}-tab`) as HTMLElement
      if (targetContent) {
        targetContent.style.display = 'block'
      }
    })
  })

  // 复制功能
  const btnCopy = modal.querySelector('.btn-copy')
  btnCopy?.addEventListener('click', async () => {
    try {
      const activeTab = modal.querySelector('.tab-btn[style*="rgb(255, 77, 79)"]')?.getAttribute('data-tab') || 'formatted'
      const dataToCopy = activeTab === 'raw' ? amazonData : dianxiaomiData
      await navigator.clipboard.writeText(JSON.stringify(dataToCopy, null, 2))
      showNotification('数据已复制到剪贴板', 'success')
    } catch (error) {
      showNotification('复制失败', 'error')
    }
  })

  // 下载功能
  const btnDownload = modal.querySelector('.btn-download')
  btnDownload?.addEventListener('click', () => {
    try {
      const activeTab = modal.querySelector('.tab-btn[style*="rgb(255, 77, 79)"]')?.getAttribute('data-tab') || 'formatted'
      const dataToCopy = activeTab === 'raw' ? amazonData : dianxiaomiData
      const dataStr = JSON.stringify(dataToCopy, null, 2)
      const blob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = url
      a.download = `amazon-data-${activeTab}-${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      showNotification('文件下载已开始', 'success')
    } catch (error) {
      showNotification('下载失败', 'error')
    }
  })
}

// 显示通知
function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
  // 创建通知元素
  const notification = document.createElement('div')
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    transform: translateX(100%);
  `

  // 设置颜色
  const colors = {
    success: '#52c41a',
    error: '#ff4d4f',
    warning: '#faad14',
    info: '#1677ff'
  }
  notification.style.background = colors[type]
  notification.textContent = message

  document.body.appendChild(notification)

  // 动画显示
  setTimeout(() => {
    notification.style.transform = 'translateX(0)'
  }, 100)

  // 自动隐藏
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      document.body.removeChild(notification)
    }, 300)
  }, 3000)
}

// 工具函数
function isAmazonSearchPage(): boolean {
  return window.location.href.includes('amazon.com/s') || 
         window.location.href.includes('amazon.com/gp/search')
}

function isAmazonProductPage(): boolean {
  return window.location.href.includes('amazon.com') && 
         (window.location.href.includes('/dp/') || window.location.href.includes('/gp/product/'))
}

function hasNextPage(): boolean {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]')
  return nextPageLink !== null
}

function goToNextPage() {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]') as HTMLAnchorElement
  if (nextPageLink) {
    window.location.href = nextPageLink.href
  }
}

// 初始化
function init() {
  // 检查是否为Amazon页面
  if (!window.location.href.includes('amazon.com')) {
    return
  }

  console.info('[AmazonCollector] 初始化Amazon采集器...')

  // 等待页面加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init)
    return
  }

  // 如果是搜索页面，创建批量采集按钮和单品采集按钮
  if (isAmazonSearchPage()) {
    const batchButton = createBatchCollectionButton()
    document.body.appendChild(batchButton)
    addProductCollectionButtons()
  }

  console.info('[AmazonCollector] Amazon采集器初始化完成')
}

// 为产品添加采集按钮
function addProductCollectionButtons() {
  const productElements = document.querySelectorAll('[data-asin]')

  productElements.forEach(productElement => {
    // 避免重复添加
    if (productElement.querySelector('.amazon-product-collect-btn')) {
      return
    }

    // 查找商品图片容器
    const imageContainer = productElement.querySelector('.s-product-image-container, .s-image-container')
    if (imageContainer) {
      // 设置图片容器为相对定位
      const container = imageContainer as HTMLElement
      container.style.position = 'relative'

      const collectButton = createProductCollectionButton(productElement)
      container.appendChild(collectButton)
    }
  })

  // 更新商品数量显示
  updateProductCount()
}

// 启动
init()

// 监听页面变化（SPA路由）
let lastUrl = location.href
new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    setTimeout(init, 1000) // 延迟重新初始化
  }
}).observe(document, { subtree: true, childList: true })
