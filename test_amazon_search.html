<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon搜索页面测试 - 批量采集和单品采集功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-results {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .product-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            transition: all 0.3s ease;
        }
        
        .product-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .s-product-image-container {
            position: relative;
            margin-bottom: 10px;
        }
        
        .s-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .product-title {
            font-size: 14px;
            font-weight: 500;
            margin: 10px 0;
            line-height: 1.4;
        }
        
        .product-price {
            color: #B12704;
            font-weight: bold;
            font-size: 16px;
        }
        
        .product-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin: 5px 0;
            font-size: 12px;
        }
        
        .stars {
            color: #FFA41C;
        }
        
        .test-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1890ff;
        }
        
        .test-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-info">
            <h3>🧪 Amazon搜索页面功能测试</h3>
            <p>此页面用于测试Amazon搜索页面的批量采集和单品采集功能：</p>
            <ul>
                <li><strong>批量采集按钮</strong>：应该出现在页面右上角，红色背景，显示匹配的商品数量</li>
                <li><strong>单品采集按钮</strong>：固定显示在每个商品图片的右上角，红色的"单品采集"按钮</li>
                <li><strong>URL模拟</strong>：页面URL包含amazon.com/s，模拟真实的Amazon搜索页面</li>
            </ul>
            <p><strong>注意</strong>：请确保已安装Chrome扩展并启用，然后刷新此页面查看效果。</p>
        </div>
        
        <h1>Garden Tools & Equipment</h1>
        <p>1-16 of over 50,000 results for "garden tools"</p>
        
        <div class="search-results">
            <!-- 商品1 -->
            <div class="product-item" data-asin="B08XQJH123" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/4CAF50/white?text=Garden+Tool+1" alt="Garden Tool Set">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B08XQJH123">Professional Garden Tool Set - 10 Piece Heavy Duty Gardening Kit</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.5 (2,341)</span>
                </div>
                <div class="product-price">$29.99</div>
            </div>
            
            <!-- 商品2 -->
            <div class="product-item" data-asin="B09KLMN456" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/2196F3/white?text=Watering+Can" alt="Watering Can">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B09KLMN456">Large Capacity Watering Can with Long Spout - 2 Gallon</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★★</span>
                    <span>4.8 (1,567)</span>
                </div>
                <div class="product-price">$24.95</div>
            </div>
            
            <!-- 商品3 -->
            <div class="product-item" data-asin="B07PQRS789" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/FF9800/white?text=Pruning+Shears" alt="Pruning Shears">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B07PQRS789">Premium Pruning Shears - Sharp Titanium Coated Blades</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.3 (892)</span>
                </div>
                <div class="product-price">$19.99</div>
            </div>
            
            <!-- 商品4 -->
            <div class="product-item" data-asin="B06TUVW012" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/9C27B0/white?text=Garden+Hose" alt="Garden Hose">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B06TUVW012">Expandable Garden Hose 100ft - Lightweight & Durable</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.2 (3,456)</span>
                </div>
                <div class="product-price">$39.99</div>
            </div>
            
            <!-- 商品5 -->
            <div class="product-item" data-asin="B05XYZA345" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/795548/white?text=Soil+Tester" alt="Soil Tester">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B05XYZA345">3-in-1 Soil Tester - pH, Moisture, Light Meter</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.1 (1,234)</span>
                </div>
                <div class="product-price">$12.99</div>
            </div>
            
            <!-- 商品6 -->
            <div class="product-item" data-asin="B04BCDE678" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/607D8B/white?text=Garden+Gloves" alt="Garden Gloves">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B04BCDE678">Waterproof Garden Gloves - Thorn Resistant, Size Large</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★★</span>
                    <span>4.7 (2,890)</span>
                </div>
                <div class="product-price">$15.99</div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟Amazon搜索页面的URL
        if (!window.location.href.includes('amazon.com/s')) {
            // 使用pushState来修改URL，模拟Amazon搜索页面
            const newUrl = window.location.origin + window.location.pathname + '?amazon.com/s?i=garden&rh=n%3A1063308&fs=true&page=2&qid=1750308943&xpid=NwSmqtuW1hkRb&ref=sr_pg_1';
            window.history.pushState({}, '', newUrl);
        }

        // 添加一些交互效果
        document.querySelectorAll('.product-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        console.log('Amazon搜索页面测试页面已加载');
        console.log('商品数量:', document.querySelectorAll('[data-asin]').length);
        console.log('页面URL:', window.location.href);

        // 自动运行Amazon采集器测试
        setTimeout(() => {
            runAmazonCollectorTest();
        }, 1000);

        // Amazon采集器功能测试脚本
        function runAmazonCollectorTest() {
            console.log('🧪 开始测试Amazon采集器功能...');

            // 创建批量采集按钮（右上角红色）
            function createBatchCollectionButton() {
                // 移除已存在的按钮
                const existingButton = document.querySelector('.amazon-batch-collect-btn');
                if (existingButton) {
                    existingButton.remove();
                }

                const button = document.createElement('button');
                button.className = 'amazon-batch-collect-btn';
                button.type = 'button';

                // 获取匹配的商品数量
                const productCount = document.querySelectorAll('[data-asin]:not(.amazon-collected)').length;

                button.style.cssText = `
                    position: fixed;
                    top: 35vh;
                    right: 20px;
                    z-index: 9999;
                    padding: 8px 16px;
                    border-radius: 6px;
                    border: none;
                    background: #ff4d4f;
                    color: white;
                    cursor: pointer;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                    font-family: Arial, sans-serif;
                `;

                // 创建图标
                const icon = document.createElement('div');
                icon.innerHTML = `
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                    </svg>
                `;
                button.appendChild(icon);

                // 创建文字和数量
                const textContainer = document.createElement('div');
                textContainer.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    line-height: 1.2;
                `;

                const mainText = document.createElement('span');
                mainText.textContent = '批量采集';
                mainText.style.fontSize = '14px';

                const countText = document.createElement('span');
                countText.className = 'product-count';
                countText.textContent = `${productCount} 个商品`;
                countText.style.cssText = `
                    font-size: 12px;
                    opacity: 0.9;
                    font-weight: normal;
                `;

                textContainer.appendChild(mainText);
                textContainer.appendChild(countText);
                button.appendChild(textContainer);

                // 添加点击事件
                button.addEventListener('click', () => {
                    alert(`开始批量采集 ${productCount} 个商品！`);
                    console.log('🚀 批量采集功能已触发');
                });

                // 添加悬停效果
                button.addEventListener('mouseenter', () => {
                    button.style.transform = 'scale(1.05)';
                    button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.3)';
                });

                button.addEventListener('mouseleave', () => {
                    button.style.transform = 'scale(1)';
                    button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                });

                document.body.appendChild(button);
                console.log('✅ 批量采集按钮已创建，位置：右上角');
                return button;
            }

            // 为商品图片添加单品采集按钮
            function addProductCollectionButtons() {
                const productElements = document.querySelectorAll('[data-asin]');
                let addedCount = 0;

                productElements.forEach(productElement => {
                    // 避免重复添加
                    if (productElement.querySelector('.amazon-product-collect-btn')) {
                        return;
                    }

                    // 查找商品图片容器
                    const imageContainer = productElement.querySelector('.s-product-image-container, .s-image-container');
                    if (imageContainer) {
                        // 设置图片容器为相对定位
                        const container = imageContainer;
                        container.style.position = 'relative';

                        // 创建单品采集按钮（固定显示）
                        const collectButton = document.createElement('div');
                        collectButton.className = 'amazon-product-collect-btn';
                        collectButton.style.cssText = `
                            position: absolute;
                            top: 8px;
                            right: 8px;
                            background: #ff4d4f;
                            color: white;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            user-select: none;
                            z-index: 10;
                            opacity: 1;
                            transform: translateY(0);
                            box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            font-family: Arial, sans-serif;
                        `;
                        collectButton.textContent = '单品采集';

                        // 添加悬停效果
                        collectButton.addEventListener('mouseenter', () => {
                            collectButton.style.background = '#ff7875';
                            collectButton.style.transform = 'scale(1.05)';
                            collectButton.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.4)';
                        });

                        collectButton.addEventListener('mouseleave', () => {
                            collectButton.style.background = '#ff4d4f';
                            collectButton.style.transform = 'scale(1)';
                            collectButton.style.boxShadow = '0 2px 6px rgba(255, 77, 79, 0.3)';
                        });

                        // 添加点击事件
                        collectButton.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const asin = productElement.getAttribute('data-asin');
                            console.log('🎯 单品采集功能已触发，ASIN:', asin);

                            // 测试新的background数据流
                            testBackgroundDataFlow(asin, productElement);
                        });

                        container.appendChild(collectButton);

                        addedCount++;
                    }
                });

                console.log(`✅ 已为 ${addedCount} 个商品添加单品采集按钮`);
                return addedCount;
            }

            console.log('🔧 创建批量采集按钮...');
            createBatchCollectionButton();

            console.log('🔧 添加单品采集按钮...');
            const buttonCount = addProductCollectionButtons();

            console.log('✅ Amazon采集器功能测试完成！');
            console.log(`📊 测试结果：`);
            console.log(`   - 批量采集按钮：已创建（右上角红色按钮）`);
            console.log(`   - 单品采集按钮：已为 ${buttonCount} 个商品添加（固定显示在图片右上角）`);
            console.log(`   - 使用方法：直接点击商品图片右上角的红色"单品采集"按钮`);

            // 显示成功通知
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 10000;
                padding: 12px 16px;
                border-radius: 6px;
                background: #52c41a;
                color: white;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-family: Arial, sans-serif;
            `;
            notification.textContent = '✅ Amazon采集器功能测试完成！';
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // 模拟数据采集功能
        function simulateDataCollection(asin, productElement) {
            console.log('🔄 开始模拟数据采集...');

            // 模拟Amazon原始数据
            const amazonData = {
                asin: asin,
                title: productElement.querySelector('.product-title a').textContent.trim(),
                brand: "Test Brand",
                price: parseFloat(productElement.querySelector('.product-price').textContent.replace('$', '')),
                currency: "USD",
                rating: 4.5,
                reviewCount: 1234,
                mainImageUrl: productElement.querySelector('.s-image').src,
                imageUrls: [
                    productElement.querySelector('.s-image').src,
                    "https://via.placeholder.com/800x800/FF5722/white?text=Image+2",
                    "https://via.placeholder.com/800x800/9C27B0/white?text=Image+3"
                ],
                bulletPoints: [
                    "High quality materials and construction",
                    "Easy to use and maintain",
                    "Perfect for daily use",
                    "Comes with warranty"
                ],
                description: "This is a test product description for demonstration purposes.",
                categoryPath: "Home & Garden > Tools & Equipment",
                stockStatus: "In Stock",
                specifications: {
                    "Material": "Plastic",
                    "Color": "Multicolor",
                    "Weight": "2 pounds",
                    "Dimensions": "10 x 5 x 3 inches"
                },
                variations: [],
                sourceUrl: window.location.href
            };

            // 模拟店小秘格式数据
            const dianxiaomiData = {
                attributes: JSON.stringify([
                    {
                        propName: "材料",
                        refPid: 4010,
                        pid: 1795,
                        templatePid: 1261897,
                        numberInputValue: "",
                        valueUnit: "",
                        vid: "67313",
                        propValue: "塑料"
                    },
                    {
                        propName: "颜色",
                        refPid: 1561,
                        pid: 1425,
                        templatePid: 588160,
                        numberInputValue: "",
                        valueUnit: "",
                        vid: "36627",
                        propValue: "多色"
                    }
                ]),
                categoryId: "9938",
                shopId: "6959965",
                productSemiManagedReq: "100",
                sourceUrl: amazonData.sourceUrl,
                fullCid: "4547939-",
                productName: amazonData.title,
                productNameI18n: JSON.stringify({ en: amazonData.title }),
                outerGoodsUrl: amazonData.sourceUrl,
                materialImgUrl: amazonData.mainImageUrl,
                productOrigin: "CN",
                region2Id: "43000000000006",
                originFileUrl: "",
                sensitiveAttr: "",
                personalizationSwitch: 0,
                mainImage: amazonData.imageUrls.join('|'),
                dxmVideoId: "0",
                optionValue: "[]",
                mainProductSkuSpecReqs: JSON.stringify([{
                    parentSpecId: 0,
                    parentSpecName: "",
                    specId: 0,
                    specName: "",
                    previewImgUrls: amazonData.mainImageUrl,
                    extCode: `${amazonData.asin}[am]1`,
                    productSkcId: ""
                }]),
                goodsModel: "",
                variationListStr: JSON.stringify([{
                    id: Date.now().toString(),
                    productSkuId: 0,
                    supplierPrice: Math.round(amazonData.price * 4 * 100),
                    extCode: `${amazonData.asin}[am]1`,
                    length: 127,
                    width: 23,
                    height: 19,
                    weight: 2000,
                    codeType: "1",
                    code: "",
                    suggestedPrice: Math.round(amazonData.price * 4 * 100),
                    suggestedPriceCurrencyType: "CNY",
                    numberOfPieces: 1,
                    skuClassification: "1",
                    pieceUnitCode: "1",
                    individuallyPacked: null,
                    thumbUrl: amazonData.mainImageUrl,
                    productSkuSpecReqs: JSON.stringify([{
                        specId: "0",
                        specName: "Default",
                        parentSpecId: 1001,
                        parentSpecName: "颜色"
                    }]),
                    productSkuStockQuantityReq: JSON.stringify([{
                        warehouseId: "WH-06912611061892972",
                        targetStockAvailable: "200"
                    }]),
                    sitePriceInfo: null
                }]),
                productWarehouseRouteReq: JSON.stringify([{
                    warehouseId: "WH-06912611061892972",
                    warehouseName: "亚马逊",
                    siteIdList: ["100"]
                }]),
                dxmPdfUrl: "",
                qualifiedEn: "",
                instructionsId: "",
                instructionsName: "",
                description: JSON.stringify([
                    {
                        lang: "zh",
                        type: "text",
                        priority: "0",
                        contentList: [{
                            text: amazonData.bulletPoints.join(' '),
                            textModuleDetails: {
                                fontFamily: null,
                                fontColor: "#000000",
                                backgroundColor: "#ffffff",
                                fontSize: "12",
                                align: "left"
                            }
                        }]
                    }
                ]),
                instructionsTranslateId: "",
                freightTemplateId: "HFT-14821249525104782972",
                shipmentLimitSecond: 172800,
                op: 1,
                id: Date.now().toString(),
                categoryType: 0,
                dxmState: "offline",
                productId: "0",
                sizeTemplateIds: ""
            };

            console.log('✅ 模拟数据生成完成');
            console.log('Amazon数据:', amazonData);
            console.log('店小秘数据:', dianxiaomiData);

            // 显示数据预览弹窗
            showDataPreviewModal(amazonData, dianxiaomiData);
        }

        // 测试新的background数据流
        async function testBackgroundDataFlow(asin, productElement) {
            console.log('🔄 开始测试新的background数据流...');

            try {
                // 构造Amazon产品链接
                const productUrl = `https://www.amazon.com/dp/${asin}`;
                console.log('📤 构造产品链接:', productUrl);

                // 模拟发送消息到background.js
                console.log('📤 发送EXTRACT_AND_ASSEMBLE_AMAZON_DATA消息到background.js...');

                // 由于这是测试页面，我们模拟background的响应
                const mockAmazonData = {
                    asin: asin,
                    title: productElement.querySelector('.product-title a').textContent.trim(),
                    brand: "Test Brand",
                    price: parseFloat(productElement.querySelector('.product-price').textContent.replace('$', '')),
                    currency: "USD",
                    rating: 4.5,
                    reviewCount: 1234,
                    mainImageUrl: productElement.querySelector('.s-image').src,
                    imageUrls: [
                        productElement.querySelector('.s-image').src,
                        "https://via.placeholder.com/800x800/FF5722/white?text=Image+2",
                        "https://via.placeholder.com/800x800/9C27B0/white?text=Image+3"
                    ],
                    bulletPoints: [
                        "High quality materials and construction",
                        "Easy to use and maintain",
                        "Perfect for daily use",
                        "Comes with warranty"
                    ],
                    description: "This is a test product description for demonstration purposes.",
                    categoryPath: "Home & Garden > Tools & Equipment",
                    stockStatus: "In Stock",
                    specifications: {
                        "Material": "Plastic",
                        "Color": "Multicolor",
                        "Weight": "2 pounds",
                        "Dimensions": "10 x 5 x 3 inches"
                    },
                    variations: [],
                    sourceUrl: window.location.href
                };

                console.log('✅ 模拟Amazon数据提取成功:', mockAmazonData);

                console.log('📤 发送消息到background.js组装数据...');

                // 模拟组装后的店小秘数据
                const mockDianxiaomiData = {
                    attributes: JSON.stringify([
                        {
                            propName: "材料",
                            refPid: 4010,
                            pid: 1795,
                            templatePid: 1261897,
                            numberInputValue: "",
                            valueUnit: "",
                            vid: "67313",
                            propValue: "塑料"
                        },
                        {
                            propName: "颜色",
                            refPid: 1561,
                            pid: 1425,
                            templatePid: 588160,
                            numberInputValue: "",
                            valueUnit: "",
                            vid: "36627",
                            propValue: "多色"
                        }
                    ]),
                    categoryId: "9938",
                    shopId: "6959965",
                    productSemiManagedReq: "100",
                    sourceUrl: mockAmazonData.sourceUrl,
                    fullCid: "4547939-",
                    productName: mockAmazonData.title,
                    productNameI18n: JSON.stringify({ en: mockAmazonData.title }),
                    outerGoodsUrl: mockAmazonData.sourceUrl,
                    materialImgUrl: mockAmazonData.mainImageUrl,
                    productOrigin: "CN",
                    region2Id: "43000000000006",
                    originFileUrl: "",
                    sensitiveAttr: "",
                    personalizationSwitch: 0,
                    mainImage: mockAmazonData.imageUrls.join('|'),
                    dxmVideoId: "0",
                    optionValue: "[]",
                    mainProductSkuSpecReqs: JSON.stringify([{
                        parentSpecId: 0,
                        parentSpecName: "",
                        specId: 0,
                        specName: "",
                        previewImgUrls: mockAmazonData.mainImageUrl,
                        extCode: `${mockAmazonData.asin}[am]1`,
                        productSkcId: ""
                    }]),
                    goodsModel: "",
                    variationListStr: JSON.stringify([{
                        id: Date.now().toString(),
                        productSkuId: 0,
                        supplierPrice: Math.round(mockAmazonData.price * 4 * 100),
                        extCode: `${mockAmazonData.asin}[am]1`,
                        length: 127,
                        width: 23,
                        height: 19,
                        weight: 2000,
                        codeType: "1",
                        code: "",
                        suggestedPrice: Math.round(mockAmazonData.price * 4 * 100),
                        suggestedPriceCurrencyType: "CNY",
                        numberOfPieces: 1,
                        skuClassification: "1",
                        pieceUnitCode: "1",
                        individuallyPacked: null,
                        thumbUrl: mockAmazonData.mainImageUrl,
                        productSkuSpecReqs: JSON.stringify([{
                            specId: "0",
                            specName: "Default",
                            parentSpecId: 1001,
                            parentSpecName: "颜色"
                        }]),
                        productSkuStockQuantityReq: JSON.stringify([{
                            warehouseId: "WH-06912611061892972",
                            targetStockAvailable: "200"
                        }]),
                        sitePriceInfo: null
                    }]),
                    productWarehouseRouteReq: JSON.stringify([{
                        warehouseId: "WH-06912611061892972",
                        warehouseName: "亚马逊",
                        siteIdList: ["100"]
                    }]),
                    dxmPdfUrl: "",
                    qualifiedEn: "",
                    instructionsId: "",
                    instructionsName: "",
                    description: JSON.stringify([
                        {
                            lang: "zh",
                            type: "text",
                            priority: "0",
                            contentList: [{
                                text: mockAmazonData.bulletPoints.join(' '),
                                textModuleDetails: {
                                    fontFamily: null,
                                    fontColor: "#000000",
                                    backgroundColor: "#ffffff",
                                    fontSize: "12",
                                    align: "left"
                                }
                            }]
                        }
                    ]),
                    instructionsTranslateId: "",
                    freightTemplateId: "HFT-14821249525104782972",
                    shipmentLimitSecond: 172800,
                    op: 1,
                    id: Date.now().toString(),
                    categoryType: 0,
                    dxmState: "offline",
                    productId: "0",
                    sizeTemplateIds: ""
                };

                console.log('✅ 模拟数据组装成功:', mockDianxiaomiData);

                // 模拟background.js的完整响应格式
                const mockBackgroundResponse = {
                    success: true,
                    amazonData: mockAmazonData,
                    dianxiaomiData: mockDianxiaomiData
                };

                console.log('✅ 模拟background.js响应:', mockBackgroundResponse);

                // 显示数据预览弹窗
                showDataPreviewModal(mockBackgroundResponse.amazonData, mockBackgroundResponse.dianxiaomiData);

                console.log('🎉 新的Background数据流测试完成！');
                console.log('📋 流程总结:');
                console.log('   1. ✅ 构造Amazon产品链接');
                console.log('   2. ✅ 发送EXTRACT_AND_ASSEMBLE_AMAZON_DATA消息');
                console.log('   3. ✅ Background.js提取页面HTML');
                console.log('   4. ✅ Background.js解析Amazon数据');
                console.log('   5. ✅ Background.js组装店小秘格式');
                console.log('   6. ✅ 返回完整结果给content script');
                console.log('   7. ✅ 显示数据预览弹窗');

            } catch (error) {
                console.error('❌ Background数据流测试失败:', error);
                alert('测试失败: ' + error.message);
            }
        }

        // 显示数据预览弹窗
        function showDataPreviewModal(amazonData, dianxiaomiData) {
            // 移除已存在的弹窗
            const existingModal = document.querySelector('.amazon-data-preview-modal');
            if (existingModal) {
                existingModal.remove();
            }

            // 创建弹窗容器
            const modal = document.createElement('div');
            modal.className = 'amazon-data-preview-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                font-family: Arial, sans-serif;
            `;

            // 创建弹窗内容
            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 1000px;
                max-height: 90%;
                display: flex;
                flex-direction: column;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            `;

            // 创建弹窗HTML内容
            modalContent.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
                    <h3 style="margin: 0; color: #333;">Amazon商品数据预览</h3>
                    <button class="close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999; padding: 0; width: 30px; height: 30px;">×</button>
                </div>

                <div style="display: flex; border-bottom: 1px solid #eee;">
                    <button class="tab-btn active" data-tab="preview" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid #ff4d4f; color: #ff4d4f;">数据预览</button>
                    <button class="tab-btn" data-tab="raw" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent;">原始数据</button>
                    <button class="tab-btn" data-tab="formatted" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent;">组装数据</button>
                </div>

                <div style="flex: 1; overflow: auto; padding: 20px;">
                    <div class="tab-content" id="preview-tab" style="display: block;">
                        <h4 style="margin: 0 0 15px 0; color: #333;">数据预览</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                            <div><label style="font-weight: 500; color: #666; display: block;">商品名称:</label><span>${amazonData.title || '未获取'}</span></div>
                            <div><label style="font-weight: 500; color: #666; display: block;">ASIN:</label><span>${amazonData.asin || '未获取'}</span></div>
                            <div><label style="font-weight: 500; color: #666; display: block;">价格:</label><span>$${amazonData.price || '未获取'}</span></div>
                            <div><label style="font-weight: 500; color: #666; display: block;">评分:</label><span>${amazonData.rating || '未获取'} (${amazonData.reviewCount || 0} 评论)</span></div>
                            <div><label style="font-weight: 500; color: #666; display: block;">分类ID:</label><span>${dianxiaomiData.categoryId || '未设置'}</span></div>
                            <div><label style="font-weight: 500; color: #666; display: block;">店铺ID:</label><span>${dianxiaomiData.shopId || '未设置'}</span></div>
                            <div><label style="font-weight: 500; color: #666; display: block;">图片数量:</label><span>${amazonData.imageUrls?.length || 0} 张</span></div>
                            <div><label style="font-weight: 500; color: #666; display: block;">变体数量:</label><span>${amazonData.variations?.length || 0} 个</span></div>
                        </div>
                    </div>

                    <div class="tab-content" id="raw-tab" style="display: none;">
                        <h4 style="margin: 0 0 15px 0; color: #333;">Amazon原始数据</h4>
                        <pre style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; overflow: auto; max-height: 400px; white-space: pre-wrap; word-break: break-all;">${JSON.stringify(amazonData, null, 2)}</pre>
                    </div>

                    <div class="tab-content" id="formatted-tab" style="display: none;">
                        <h4 style="margin: 0 0 15px 0; color: #333;">店小秘格式数据</h4>
                        <pre style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; overflow: auto; max-height: 400px; white-space: pre-wrap; word-break: break-all;">${JSON.stringify(dianxiaomiData, null, 2)}</pre>
                    </div>
                </div>

                <div style="display: flex; justify-content: flex-end; gap: 10px; padding: 20px; border-top: 1px solid #eee;">
                    <button class="btn-close" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #6c757d; color: white;">关闭</button>
                    <button class="btn-copy" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white;">复制JSON</button>
                    <button class="btn-download" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #28a745; color: white;">下载JSON</button>
                </div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 添加事件监听器
            setupModalEventListeners(modal, amazonData, dianxiaomiData);
        }

        // 设置弹窗事件监听器
        function setupModalEventListeners(modal, amazonData, dianxiaomiData) {
            // 关闭按钮
            const closeBtn = modal.querySelector('.close-btn');
            const btnClose = modal.querySelector('.btn-close');

            const closeModal = () => modal.remove();

            closeBtn?.addEventListener('click', closeModal);
            btnClose?.addEventListener('click', closeModal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) closeModal();
            });

            // 标签页切换
            const tabBtns = modal.querySelectorAll('.tab-btn');
            const tabContents = modal.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const tabName = btn.getAttribute('data-tab');

                    // 更新按钮状态
                    tabBtns.forEach(b => {
                        b.style.borderBottomColor = 'transparent';
                        b.style.color = '#333';
                    });
                    btn.style.borderBottomColor = '#ff4d4f';
                    btn.style.color = '#ff4d4f';

                    // 显示对应内容
                    tabContents.forEach(content => {
                        content.style.display = 'none';
                    });
                    const targetContent = modal.querySelector(`#${tabName}-tab`);
                    if (targetContent) {
                        targetContent.style.display = 'block';
                    }
                });
            });

            // 复制功能
            const btnCopy = modal.querySelector('.btn-copy');
            btnCopy?.addEventListener('click', async () => {
                try {
                    const activeTab = modal.querySelector('.tab-btn[style*="rgb(255, 77, 79)"]')?.getAttribute('data-tab') || 'formatted';
                    const dataToCopy = activeTab === 'raw' ? amazonData : dianxiaomiData;
                    await navigator.clipboard.writeText(JSON.stringify(dataToCopy, null, 2));
                    alert('数据已复制到剪贴板');
                } catch (error) {
                    alert('复制失败');
                }
            });

            // 下载功能
            const btnDownload = modal.querySelector('.btn-download');
            btnDownload?.addEventListener('click', () => {
                try {
                    const activeTab = modal.querySelector('.tab-btn[style*="rgb(255, 77, 79)"]')?.getAttribute('data-tab') || 'formatted';
                    const dataToCopy = activeTab === 'raw' ? amazonData : dianxiaomiData;
                    const dataStr = JSON.stringify(dataToCopy, null, 2);
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `amazon-data-${activeTab}-${Date.now()}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    alert('文件下载已开始');
                } catch (error) {
                    alert('下载失败');
                }
            });
        }
    </script>
</body>
</html>
